import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![undo](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTUxMS40IDEyNEMyOTAuNSAxMjQuMyAxMTIgMzAzIDExMiA1MjMuOWMwIDEyOCA2MC4yIDI0MiAxNTMuOCAzMTUuMmwtMzcuNSA0OGMtNC4xIDUuMy0uMyAxMyA2LjMgMTIuOWwxNjctLjhjNS4yIDAgOS00LjkgNy43LTkuOUwzNjkuOCA3MjdhOCA4IDAgMDAtMTQuMS0zTDMxNSA3NzYuMWMtMTAuMi04LTIwLTE2LjctMjkuMy0yNmEzMTguNjQgMzE4LjY0IDAgMDEtNjguNi0xMDEuN0MyMDAuNCA2MDkgMTkyIDU2Ny4xIDE5MiA1MjMuOXM4LjQtODUuMSAyNS4xLTEyNC41YzE2LjEtMzguMSAzOS4yLTcyLjMgNjguNi0xMDEuNyAyOS40LTI5LjQgNjMuNi01Mi41IDEwMS43LTY4LjZDNDI2LjkgMjEyLjQgNDY4LjggMjA0IDUxMiAyMDRzODUuMSA4LjQgMTI0LjUgMjUuMWMzOC4xIDE2LjEgNzIuMyAzOS4yIDEwMS43IDY4LjYgMjkuNCAyOS40IDUyLjUgNjMuNiA2OC42IDEwMS43IDE2LjcgMzkuNCAyNS4xIDgxLjMgMjUuMSAxMjQuNXMtOC40IDg1LjEtMjUuMSAxMjQuNWEzMTguNjQgMzE4LjY0IDAgMDEtNjguNiAxMDEuN2MtNy41IDcuNS0xNS4zIDE0LjUtMjMuNCAyMS4yYTcuOTMgNy45MyAwIDAwLTEuMiAxMS4xbDM5LjQgNTAuNWMyLjggMy41IDcuOSA0LjEgMTEuNCAxLjNDODU0LjUgNzYwLjggOTEyIDY0OS4xIDkxMiA1MjMuOWMwLTIyMS4xLTE3OS40LTQwMC4yLTQwMC42LTM5OS45eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
