import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![wallet](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg4MCAxMTJIMTQ0Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3MzZjMCAxNy43IDE0LjMgMzIgMzIgMzJoNzM2YzE3LjcgMCAzMi0xNC4zIDMyLTMyVjE0NGMwLTE3LjctMTQuMy0zMi0zMi0zMnptLTQwIDQ2NEg1MjhWNDQ4aDMxMnYxMjh6bTAtMTkySDQ5NmMtMTcuNyAwLTMyIDE0LjMtMzIgMzJ2MTkyYzAgMTcuNyAxNC4zIDMyIDMyIDMyaDM0NHYyMDBIMTg0VjE4NGg2NTZ2MjAweiIgZmlsbD0iIzE2NzdmZiIgLz48cGF0aCBkPSJNNTI4IDU3NmgzMTJWNDQ4SDUyOHYxMjh6bTkyLTEwNGMyMi4xIDAgNDAgMTcuOSA0MCA0MHMtMTcuOSA0MC00MCA0MC00MC0xNy45LTQwLTQwIDE3LjktNDAgNDAtNDB6IiBmaWxsPSIjZTZmNGZmIiAvPjxwYXRoIGQ9Ik01ODAgNTEyYTQwIDQwIDAgMTA4MCAwIDQwIDQwIDAgMTAtODAgMHoiIGZpbGw9IiMxNjc3ZmYiIC8+PHBhdGggZD0iTTE4NCA4NDBoNjU2VjY0MEg0OTZjLTE3LjcgMC0zMi0xNC4zLTMyLTMyVjQxNmMwLTE3LjcgMTQuMy0zMiAzMi0zMmgzNDRWMTg0SDE4NHY2NTZ6IiBmaWxsPSIjZTZmNGZmIiAvPjwvc3ZnPg==) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
