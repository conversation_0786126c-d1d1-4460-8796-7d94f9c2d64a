import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![solution](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTY4OCAyNjRjMC00LjQtMy42LTgtOC04SDI5NmMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgzODRjNC40IDAgOC0zLjYgOC04di00OHptLTggMTM2SDI5NmMtNC40IDAtOCAzLjYtOCA4djQ4YzAgNC40IDMuNiA4IDggOGgzODRjNC40IDAgOC0zLjYgOC04di00OGMwLTQuNC0zLjYtOC04LTh6TTQ4MCA1NDRIMjk2Yy00LjQgMC04IDMuNi04IDh2NDhjMCA0LjQgMy42IDggOCA4aDE4NGM0LjQgMCA4LTMuNiA4LTh2LTQ4YzAtNC40LTMuNi04LTgtOHptLTQ4IDMwOEgyMDhWMTQ4aDU2MHYzNDRjMCA0LjQgMy42IDggOCA4aDU2YzQuNCAwIDgtMy42IDgtOFYxMDhjMC0xNy43LTE0LjMtMzItMzItMzJIMTY4Yy0xNy43IDAtMzIgMTQuMy0zMiAzMnY3ODRjMCAxNy43IDE0LjMgMzIgMzIgMzJoMjY0YzQuNCAwIDgtMy42IDgtOHYtNTZjMC00LjQtMy42LTgtOC04em0zNTYuOC03NC40YzI5LTI2LjMgNDcuMi02NC4zIDQ3LjItMTA2LjYgMC03OS41LTY0LjUtMTQ0LTE0NC0xNDRzLTE0NCA2NC41LTE0NCAxNDRjMCA0Mi4zIDE4LjIgODAuMyA0Ny4yIDEwNi42LTU3IDMyLjUtOTYuMiA5Mi43LTk5LjIgMTYyLjEtLjIgNC41IDMuNSA4LjMgOCA4LjNoNDguMWM0LjIgMCA3LjctMy4zIDgtNy42QzU2NCA4NzEuMiA2MjEuNyA4MTYgNjkyIDgxNnMxMjggNTUuMiAxMzEuOSAxMjQuNGMuMiA0LjIgMy43IDcuNiA4IDcuNkg4ODBjNC42IDAgOC4yLTMuOCA4LTguMy0yLjktNjkuNS00Mi4yLTEyOS42LTk5LjItMTYyLjF6TTY5MiA1OTFjNDQuMiAwIDgwIDM1LjggODAgODBzLTM1LjggODAtODAgODAtODAtMzUuOC04MC04MCAzNS44LTgwIDgwLTgweiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
