#!/usr/bin/env python3
# 检查数据库表结构和数据

import sqlite3
import pandas as pd
import os

def check_database():
    db_path = 'data/fucai3d.db'
    
    if not os.path.exists(db_path):
        print(f"数据库文件不存在: {db_path}")
        return
    
    print(f"检查数据库: {db_path}")
    
    try:
        conn = sqlite3.connect(db_path)
        
        # 检查所有表
        tables = pd.read_sql_query("SELECT name FROM sqlite_master WHERE type='table'", conn)
        print("\n数据库中的表:")
        for table in tables['name']:
            print(f"  - {table}")
        
        # 检查final_predictions表
        if 'final_predictions' in tables['name'].values:
            print("\n=== final_predictions表分析 ===")
            
            # 表结构
            schema = pd.read_sql_query("PRAGMA table_info(final_predictions)", conn)
            print("\n表结构:")
            for _, row in schema.iterrows():
                print(f"  {row['name']}: {row['type']} (nullable: {row['notnull'] == 0})")
            
            # 数据数量
            count_result = pd.read_sql_query("SELECT COUNT(*) as count FROM final_predictions", conn)
            count = count_result.iloc[0]['count']
            print(f"\n数据数量: {count}")
            
            if count > 0:
                # 最新数据
                latest = pd.read_sql_query("SELECT * FROM final_predictions ORDER BY created_at DESC LIMIT 3", conn)
                print("\n最新3条数据:")
                print(latest.to_string())
                
                # 检查数据完整性
                null_check = pd.read_sql_query("""
                    SELECT 
                        SUM(CASE WHEN issue IS NULL THEN 1 ELSE 0 END) as null_issue,
                        SUM(CASE WHEN hundreds IS NULL THEN 1 ELSE 0 END) as null_hundreds,
                        SUM(CASE WHEN tens IS NULL THEN 1 ELSE 0 END) as null_tens,
                        SUM(CASE WHEN units IS NULL THEN 1 ELSE 0 END) as null_units
                    FROM final_predictions
                """, conn)
                print("\n数据完整性检查:")
                print(null_check.to_string())
            else:
                print("表中没有数据!")
        else:
            print("\nfinal_predictions表不存在!")
            
            # 检查是否有其他预测相关的表
            prediction_tables = [t for t in tables['name'] if 'prediction' in t.lower()]
            if prediction_tables:
                print("\n找到其他预测相关的表:")
                for table in prediction_tables:
                    print(f"  - {table}")
        
        conn.close()
        
    except Exception as e:
        print(f"检查数据库时出错: {e}")

if __name__ == "__main__":
    check_database()
