import sqlite3
import os

# 检查数据库
db_path = "data/fucai3d.db"
print("=== 数据库检查 ===")
print(f"数据库路径: {db_path}")
print(f"文件存在: {os.path.exists(db_path)}")

if os.path.exists(db_path):
    try:
        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        
        # 查看所有表
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
        tables = cursor.fetchall()
        print(f"所有表: {[t[0] for t in tables]}")
        
        # 检查final_predictions表
        if ('final_predictions',) in tables:
            cursor.execute("SELECT COUNT(*) FROM final_predictions")
            count = cursor.fetchone()[0]
            print(f"final_predictions表记录数: {count}")
            
            if count > 0:
                cursor.execute("SELECT * FROM final_predictions LIMIT 2")
                rows = cursor.fetchall()
                print(f"示例记录: {rows}")
        else:
            print("final_predictions表不存在!")
            
            # 检查其他可能的表
            for table_name in ['predictions', 'lottery_data', 'lottery_results']:
                if (table_name,) in tables:
                    cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                    count = cursor.fetchone()[0]
                    print(f"{table_name}表记录数: {count}")
                    
                    if count > 0:
                        cursor.execute(f"PRAGMA table_info({table_name})")
                        columns = cursor.fetchall()
                        print(f"{table_name}表结构: {[col[1] for col in columns]}")
        
        conn.close()
        
    except Exception as e:
        print(f"数据库操作错误: {e}")
else:
    print("数据库文件不存在!")
