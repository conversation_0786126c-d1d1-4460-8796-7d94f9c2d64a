import React, { useState, useEffect } from 'react'
import { Card, Row, Col, Select, DatePicker, Spin, Alert, Statistic, Table } from 'antd'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, BarChart, Bar, <PERSON><PERSON>hart, Pie, Cell } from 'recharts'
import * as Icons from '@ant-design/icons'

const { TrendingUpOutlined, Bar<PERSON><PERSON>Outlined, Pie<PERSON>hartOutlined, TableOutlined } = Icons
import axios from 'axios'
import dayjs from 'dayjs'

const { Option } = Select
const { RangePicker } = DatePicker

interface TrendData {
  date: string
  total_predictions: number
  avg_probability: number
  high_confidence_count: number
  avg_constraint_score: number
}

interface PerformanceData {
  model_name: string
  avg_accuracy: number
  avg_precision: number
  avg_recall: number
  avg_f1: number
  prediction_count: number
}

const HistoryAnalysis: React.FC = () => {
  const [trendData, setTrendData] = useState<TrendData[]>([])
  const [performanceData, setPerformanceData] = useState<PerformanceData[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState(30) // 天数
  const [selectedMetric, setSelectedMetric] = useState('accuracy')
  const [selectedPeriod, setSelectedPeriod] = useState('month')

  useEffect(() => {
    fetchAnalysisData()
  }, [timeRange, selectedMetric, selectedPeriod])

  const fetchAnalysisData = async () => {
    try {
      setLoading(true)
      setError(null)

      // 并行获取趋势数据和性能对比数据
      const [trendsResponse, performanceResponse] = await Promise.all([
        axios.get(`/api/prediction/accuracy-trends?days=${timeRange}`),
        axios.get(`/api/prediction/performance-comparison?period=${selectedPeriod}&metric=${selectedMetric}`)
      ])

      if (trendsResponse.data.status === 'success') {
        setTrendData(trendsResponse.data.data)
      }

      if (performanceResponse.data.status === 'success') {
        setPerformanceData(performanceResponse.data.data)
      }

    } catch (err) {
      setError('获取历史分析数据失败')
      console.error('获取历史分析数据失败:', err)
    } finally {
      setLoading(false)
    }
  }

  const formatTrendData = (data: TrendData[]) => {
    return data.map(item => ({
      ...item,
      date: dayjs(item.date).format('MM-DD'),
      avg_probability_percent: (item.avg_probability * 100).toFixed(1),
      avg_constraint_score_percent: (item.avg_constraint_score * 100).toFixed(1)
    })).reverse() // 按时间正序排列
  }

  const getConfidenceDistribution = () => {
    if (trendData.length === 0) return []

    const totalPredictions = trendData.reduce((sum, item) => sum + item.total_predictions, 0)
    const totalHighConfidence = trendData.reduce((sum, item) => sum + item.high_confidence_count, 0)
    const mediumConfidence = Math.floor(totalPredictions * 0.4) // 假设40%为中等置信度
    const lowConfidence = totalPredictions - totalHighConfidence - mediumConfidence

    return [
      { name: '高置信度', value: totalHighConfidence, color: '#52c41a' },
      { name: '中等置信度', value: mediumConfidence, color: '#faad14' },
      { name: '低置信度', value: lowConfidence, color: '#ff4d4f' }
    ]
  }

  const performanceColumns = [
    {
      title: '模型名称',
      dataIndex: 'model_name',
      key: 'model_name',
    },
    {
      title: '准确率',
      dataIndex: 'avg_accuracy',
      key: 'accuracy',
      render: (value: number) => `${(value * 100).toFixed(1)}%`,
      sorter: (a: PerformanceData, b: PerformanceData) => a.avg_accuracy - b.avg_accuracy,
    },
    {
      title: '精确率',
      dataIndex: 'avg_precision',
      key: 'precision',
      render: (value: number) => `${(value * 100).toFixed(1)}%`,
      sorter: (a: PerformanceData, b: PerformanceData) => a.avg_precision - b.avg_precision,
    },
    {
      title: '召回率',
      dataIndex: 'avg_recall',
      key: 'recall',
      render: (value: number) => `${(value * 100).toFixed(1)}%`,
      sorter: (a: PerformanceData, b: PerformanceData) => a.avg_recall - b.avg_recall,
    },
    {
      title: 'F1分数',
      dataIndex: 'avg_f1',
      key: 'f1',
      render: (value: number) => `${(value * 100).toFixed(1)}%`,
      sorter: (a: PerformanceData, b: PerformanceData) => a.avg_f1 - b.avg_f1,
    },
    {
      title: '预测次数',
      dataIndex: 'prediction_count',
      key: 'count',
      sorter: (a: PerformanceData, b: PerformanceData) => a.prediction_count - b.prediction_count,
    },
  ]

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      return (
        <div style={{
          background: 'white',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          padding: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
        }}>
          <p style={{ margin: 0, fontWeight: 'bold' }}>日期: {label}</p>
          {payload.map((entry: any, index: number) => (
            <p key={index} style={{ margin: '4px 0 0 0', color: entry.color }}>
              {entry.name}: {entry.value}
            </p>
          ))}
        </div>
      )
    }
    return null
  }

  if (loading) {
    return (
      <div style={{ textAlign: 'center', padding: '50px' }}>
        <Spin size="large" />
        <p style={{ marginTop: 16 }}>正在加载历史分析数据...</p>
      </div>
    )
  }

  if (error) {
    return (
      <Alert
        message="历史分析数据加载失败"
        description={error}
        type="error"
        showIcon
      />
    )
  }

  const chartData = formatTrendData(trendData)
  const confidenceData = getConfidenceDistribution()

  // 计算统计指标
  const totalPredictions = trendData.reduce((sum, item) => sum + item.total_predictions, 0)
  const avgProbability = trendData.length > 0 
    ? (trendData.reduce((sum, item) => sum + item.avg_probability, 0) / trendData.length * 100).toFixed(1)
    : '0'
  const avgConstraintScore = trendData.length > 0
    ? (trendData.reduce((sum, item) => sum + item.avg_constraint_score, 0) / trendData.length * 100).toFixed(1)
    : '0'

  return (
    <div>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 24 }}>
        <h2 style={{ margin: 0 }}>📊 历史数据分析</h2>
        <div style={{ display: 'flex', gap: 16 }}>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            style={{ width: 120 }}
          >
            <Option value={7}>最近7天</Option>
            <Option value={30}>最近30天</Option>
            <Option value={90}>最近90天</Option>
            <Option value={365}>最近一年</Option>
          </Select>
          <Select
            value={selectedPeriod}
            onChange={setSelectedPeriod}
            style={{ width: 100 }}
          >
            <Option value="week">周</Option>
            <Option value="month">月</Option>
            <Option value="quarter">季度</Option>
          </Select>
        </div>
      </div>

      {/* 统计概览 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="总预测次数"
              value={totalPredictions}
              prefix={<BarChartOutlined />}
              valueStyle={{ color: '#1890ff' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均概率"
              value={avgProbability}
              suffix="%"
              prefix={<TrendingUpOutlined />}
              valueStyle={{ color: '#52c41a' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="平均约束分"
              value={avgConstraintScore}
              suffix="%"
              prefix={<PieChartOutlined />}
              valueStyle={{ color: '#722ed1' }}
            />
          </Card>
        </Col>
        <Col xs={24} sm={6}>
          <Card>
            <Statistic
              title="分析天数"
              value={timeRange}
              suffix="天"
              prefix={<TableOutlined />}
              valueStyle={{ color: '#fa8c16' }}
            />
          </Card>
        </Col>
      </Row>

      {/* 趋势图表 */}
      <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
        <Col xs={24} lg={16}>
          <Card title="📈 预测趋势分析" size="small">
            <div style={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <LineChart data={chartData}>
                  <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                  <XAxis dataKey="date" tick={{ fontSize: 12 }} />
                  <YAxis tick={{ fontSize: 12 }} />
                  <Tooltip content={<CustomTooltip />} />
                  <Line 
                    type="monotone" 
                    dataKey="total_predictions" 
                    stroke="#1890ff" 
                    name="预测次数"
                    strokeWidth={2}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="avg_probability_percent" 
                    stroke="#52c41a" 
                    name="平均概率(%)"
                    strokeWidth={2}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="high_confidence_count" 
                    stroke="#faad14" 
                    name="高置信度数量"
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>

        <Col xs={24} lg={8}>
          <Card title="🎯 置信度分布" size="small">
            <div style={{ height: 300 }}>
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={confidenceData}
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    dataKey="value"
                    label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  >
                    {confidenceData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                  <Tooltip />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </Card>
        </Col>
      </Row>

      {/* 模型性能对比 */}
      <Card title="🏆 模型性能对比" size="small">
        <Table
          columns={performanceColumns}
          dataSource={performanceData}
          rowKey="model_name"
          pagination={false}
          size="small"
        />
      </Card>
    </div>
  )
}

export default HistoryAnalysis
