# 缓存管理API路由
# 为P10-Web界面系统提供缓存管理接口

from fastapi import APIRouter, HTTPException
from typing import Dict, Any, Optional
import asyncio
from datetime import datetime

from ..cache_manager import cache_manager, cache

router = APIRouter(prefix="/api/cache", tags=["缓存管理"])

@router.get("/stats", summary="获取缓存统计信息")
async def get_cache_stats():
    """获取缓存统计信息"""
    try:
        stats = cache_manager.get_cache_stats()
        
        return {
            "status": "success",
            "data": {
                **stats,
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缓存统计失败: {str(e)}")

@router.post("/clear", summary="清理缓存")
async def clear_cache(pattern: Optional[str] = None):
    """清理缓存"""
    try:
        cleared_count = cache_manager.clear_cache(pattern)
        
        return {
            "status": "success",
            "data": {
                "cleared_count": cleared_count,
                "pattern": pattern,
                "timestamp": datetime.now().isoformat()
            },
            "message": f"已清理 {cleared_count} 个缓存项"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理缓存失败: {str(e)}")

@router.post("/cleanup", summary="清理过期缓存")
async def cleanup_expired_cache():
    """清理过期缓存"""
    try:
        expired_count = cache_manager.cleanup_expired()
        
        return {
            "status": "success",
            "data": {
                "expired_count": expired_count,
                "timestamp": datetime.now().isoformat()
            },
            "message": f"已清理 {expired_count} 个过期缓存项"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"清理过期缓存失败: {str(e)}")

@router.post("/warmup", summary="缓存预热")
async def warmup_cache():
    """缓存预热"""
    try:
        await cache_manager.warm_up_cache()
        
        return {
            "status": "success",
            "data": {
                "timestamp": datetime.now().isoformat()
            },
            "message": "缓存预热完成"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"缓存预热失败: {str(e)}")

@router.get("/health", summary="缓存健康检查")
async def cache_health_check():
    """缓存健康检查"""
    try:
        stats = cache_manager.get_cache_stats()
        
        # 健康状态评估
        health_status = "healthy"
        issues = []
        
        # 检查命中率
        if stats["hit_rate"] < 50:
            health_status = "warning"
            issues.append("缓存命中率较低")
        
        # 检查内存使用
        if stats["memory_usage"] > 10 * 1024 * 1024:  # 10MB
            health_status = "warning"
            issues.append("缓存内存使用过高")
        
        # 检查缓存大小
        if stats["size"] > stats["max_size"] * 0.9:
            health_status = "warning"
            issues.append("缓存接近容量上限")
        
        return {
            "status": "success",
            "data": {
                "health_status": health_status,
                "issues": issues,
                "stats": stats,
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"缓存健康检查失败: {str(e)}")

@router.get("/keys", summary="获取缓存键列表")
async def get_cache_keys(pattern: Optional[str] = None, limit: int = 100):
    """获取缓存键列表"""
    try:
        all_keys = list(cache.cache.keys())
        
        # 按模式过滤
        if pattern:
            filtered_keys = [key for key in all_keys if pattern in key]
        else:
            filtered_keys = all_keys
        
        # 限制返回数量
        limited_keys = filtered_keys[:limit]
        
        # 获取键的详细信息
        key_details = []
        for key in limited_keys:
            item = cache.cache.get(key)
            if item:
                key_details.append({
                    "key": key,
                    "created_at": datetime.fromtimestamp(item.created_at).isoformat(),
                    "ttl": item.ttl,
                    "access_count": item.access_count,
                    "last_access": datetime.fromtimestamp(item.last_access).isoformat(),
                    "is_expired": item.is_expired(),
                    "data_size": len(str(item.data))
                })
        
        return {
            "status": "success",
            "data": {
                "keys": key_details,
                "total_keys": len(all_keys),
                "filtered_keys": len(filtered_keys),
                "returned_keys": len(limited_keys),
                "pattern": pattern,
                "limit": limit,
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缓存键失败: {str(e)}")

@router.delete("/key/{key}", summary="删除特定缓存键")
async def delete_cache_key(key: str):
    """删除特定缓存键"""
    try:
        deleted = cache_manager.cache.delete(key)
        
        return {
            "status": "success",
            "data": {
                "key": key,
                "deleted": deleted,
                "timestamp": datetime.now().isoformat()
            },
            "message": f"缓存键 {key} {'已删除' if deleted else '不存在'}"
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"删除缓存键失败: {str(e)}")

@router.get("/performance", summary="获取缓存性能指标")
async def get_cache_performance():
    """获取缓存性能指标"""
    try:
        stats = cache_manager.get_cache_stats()
        
        # 计算性能指标
        total_requests = stats["hits"] + stats["misses"]
        avg_memory_per_item = stats["memory_usage"] / stats["size"] if stats["size"] > 0 else 0
        
        performance_metrics = {
            "hit_rate": stats["hit_rate"],
            "miss_rate": 100 - stats["hit_rate"],
            "total_requests": total_requests,
            "cache_efficiency": stats["hit_rate"] / 100 if stats["hit_rate"] > 0 else 0,
            "memory_efficiency": avg_memory_per_item,
            "eviction_rate": (stats["evictions"] / total_requests * 100) if total_requests > 0 else 0,
            "capacity_utilization": (stats["size"] / stats["max_size"] * 100),
            "recommendations": _generate_performance_recommendations(stats)
        }
        
        return {
            "status": "success",
            "data": {
                **performance_metrics,
                "timestamp": datetime.now().isoformat()
            }
        }
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取缓存性能指标失败: {str(e)}")

def _generate_performance_recommendations(stats: Dict[str, Any]) -> list:
    """生成性能优化建议"""
    recommendations = []
    
    # 命中率建议
    if stats["hit_rate"] < 50:
        recommendations.append({
            "type": "hit_rate",
            "priority": "high",
            "message": "缓存命中率较低，建议增加TTL或优化缓存策略",
            "action": "调整缓存时间或预加载热点数据"
        })
    
    # 内存使用建议
    if stats["memory_usage"] > 5 * 1024 * 1024:  # 5MB
        recommendations.append({
            "type": "memory",
            "priority": "medium",
            "message": "缓存内存使用较高，建议清理或增加容量",
            "action": "执行缓存清理或增加max_size"
        })
    
    # 容量建议
    if stats["size"] > stats["max_size"] * 0.8:
        recommendations.append({
            "type": "capacity",
            "priority": "medium",
            "message": "缓存容量使用率较高，可能影响性能",
            "action": "增加缓存容量或优化数据结构"
        })
    
    # 驱逐率建议
    if stats["evictions"] > stats["hits"] * 0.1:
        recommendations.append({
            "type": "eviction",
            "priority": "low",
            "message": "缓存驱逐率较高，可能需要调整策略",
            "action": "增加缓存容量或调整TTL"
        })
    
    return recommendations
