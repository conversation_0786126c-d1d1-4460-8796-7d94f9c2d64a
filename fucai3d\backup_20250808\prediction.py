# 预测API路由
# 为P10-Web界面系统提供预测数据接口

from fastapi import APIRouter, HTTPException, Query
from typing import List, Dict, Any, Optional
import sqlite3
import pandas as pd
from datetime import datetime, timedelta
import os
import sys

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))))

from cache_manager import cache_response, cache_database_query

router = APIRouter(prefix="/api/prediction", tags=["预测"])

# 数据库路径
DB_PATH = "data/fucai3d.db"

@router.get("/latest", summary="获取最新预测结果")
@cache_response(ttl=60)  # 缓存1分钟
async def get_latest_predictions(limit: int = Query(20, ge=1, le=100)):
    """获取最新的预测结果"""
    try:
        if not os.path.exists(DB_PATH):
            return {
                "status": "success",
                "data": _get_mock_predictions(limit),
                "message": "使用模拟数据（数据库不存在）"
            }
        
        conn = sqlite3.connect(DB_PATH)
        
        # 尝试从fusion_predictions表获取数据
        query = """
            SELECT issue, prediction_rank, hundreds, tens, units, sum_value, span,
                   combined_probability, confidence_level, constraint_score,
                   created_at
            FROM fusion_predictions
            WHERE issue = (SELECT MAX(issue) FROM fusion_predictions)
            ORDER BY prediction_rank
            LIMIT ?
        """
        
        try:
            predictions = pd.read_sql_query(query, conn, params=[limit])
            conn.close()
            
            if predictions.empty:
                return {
                    "status": "success",
                    "data": _get_mock_predictions(limit),
                    "message": "使用模拟数据（无预测记录）"
                }
            
            return {
                "status": "success",
                "data": predictions.to_dict('records'),
                "count": len(predictions),
                "issue": predictions.iloc[0]['issue'] if not predictions.empty else None
            }
            
        except Exception as e:
            conn.close()
            return {
                "status": "success",
                "data": _get_mock_predictions(limit),
                "message": f"使用模拟数据（查询失败: {str(e)}）"
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取预测数据失败: {str(e)}")

@router.get("/history", summary="获取历史预测数据")
async def get_prediction_history(
    days: int = Query(7, ge=1, le=30),
    page: int = Query(1, ge=1),
    page_size: int = Query(50, ge=10, le=200)
):
    """获取历史预测数据"""
    try:
        if not os.path.exists(DB_PATH):
            return {
                "status": "success",
                "data": _get_mock_history_data(days, page, page_size),
                "message": "使用模拟数据（数据库不存在）"
            }
        
        conn = sqlite3.connect(DB_PATH)
        
        # 计算日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)
        
        # 查询历史数据
        query = """
            SELECT issue, prediction_rank, hundreds, tens, units, sum_value, span,
                   combined_probability, confidence_level, constraint_score,
                   created_at
            FROM fusion_predictions
            WHERE created_at >= ? AND created_at <= ?
            ORDER BY created_at DESC, prediction_rank ASC
            LIMIT ? OFFSET ?
        """
        
        offset = (page - 1) * page_size
        
        try:
            history = pd.read_sql_query(
                query, conn, 
                params=[start_date.isoformat(), end_date.isoformat(), page_size, offset]
            )
            
            # 获取总数
            count_query = """
                SELECT COUNT(*) as total
                FROM fusion_predictions
                WHERE created_at >= ? AND created_at <= ?
            """
            total_count = pd.read_sql_query(
                count_query, conn,
                params=[start_date.isoformat(), end_date.isoformat()]
            ).iloc[0]['total']
            
            conn.close()
            
            return {
                "status": "success",
                "data": history.to_dict('records'),
                "pagination": {
                    "page": page,
                    "page_size": page_size,
                    "total": total_count,
                    "total_pages": (total_count + page_size - 1) // page_size
                }
            }
            
        except Exception as e:
            conn.close()
            return {
                "status": "success",
                "data": _get_mock_history_data(days, page, page_size),
                "message": f"使用模拟数据（查询失败: {str(e)}）"
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取历史数据失败: {str(e)}")

@router.get("/accuracy-trends", summary="获取准确率趋势数据")
async def get_accuracy_trends(days: int = Query(30, ge=1, le=365)):
    """获取准确率趋势数据"""
    try:
        if not os.path.exists(DB_PATH):
            return {
                "status": "success",
                "data": _get_mock_accuracy_trends(days),
                "message": "使用模拟数据（数据库不存在）"
            }

        conn = sqlite3.connect(DB_PATH)

        try:
            # 计算日期范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # 获取准确率趋势数据
            query = """
                SELECT
                    DATE(created_at) as date,
                    COUNT(*) as total_predictions,
                    AVG(combined_probability) as avg_probability,
                    COUNT(CASE WHEN confidence_level = 'high' THEN 1 END) as high_confidence_count,
                    AVG(constraint_score) as avg_constraint_score
                FROM fusion_predictions
                WHERE created_at >= ? AND created_at <= ?
                GROUP BY DATE(created_at)
                ORDER BY date DESC
            """

            trends = pd.read_sql_query(
                query, conn,
                params=[start_date.isoformat(), end_date.isoformat()]
            )

            conn.close()

            if trends.empty:
                return {
                    "status": "success",
                    "data": _get_mock_accuracy_trends(days),
                    "message": "使用模拟数据（无趋势数据）"
                }

            return {
                "status": "success",
                "data": trends.to_dict('records'),
                "time_range": {
                    "start": start_date.isoformat(),
                    "end": end_date.isoformat(),
                    "days": days
                }
            }

        except Exception as e:
            conn.close()
            return {
                "status": "success",
                "data": _get_mock_accuracy_trends(days),
                "message": f"使用模拟数据（查询失败: {str(e)}）"
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取准确率趋势失败: {str(e)}")

@router.get("/statistics", summary="获取预测统计信息")
@cache_response(ttl=300)  # 缓存5分钟
async def get_prediction_statistics():
    """获取预测统计信息"""
    try:
        if not os.path.exists(DB_PATH):
            return {
                "status": "success",
                "data": _get_mock_statistics(),
                "message": "使用模拟数据（数据库不存在）"
            }
        
        conn = sqlite3.connect(DB_PATH)
        
        try:
            # 获取基础统计
            stats_query = """
                SELECT 
                    COUNT(DISTINCT issue) as total_issues,
                    COUNT(*) as total_predictions,
                    AVG(combined_probability) as avg_probability,
                    MAX(created_at) as last_prediction_time
                FROM fusion_predictions
                WHERE created_at >= datetime('now', '-30 days')
            """
            
            stats = pd.read_sql_query(stats_query, conn)
            
            # 获取置信度分布
            confidence_query = """
                SELECT confidence_level, COUNT(*) as count
                FROM fusion_predictions
                WHERE created_at >= datetime('now', '-30 days')
                GROUP BY confidence_level
            """
            
            confidence_dist = pd.read_sql_query(confidence_query, conn)
            
            conn.close()
            
            return {
                "status": "success",
                "data": {
                    "basic_stats": stats.to_dict('records')[0] if not stats.empty else {},
                    "confidence_distribution": confidence_dist.to_dict('records'),
                    "update_time": datetime.now().isoformat()
                }
            }
            
        except Exception as e:
            conn.close()
            return {
                "status": "success",
                "data": _get_mock_statistics(),
                "message": f"使用模拟数据（查询失败: {str(e)}）"
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取统计信息失败: {str(e)}")

@router.get("/probability-distribution", summary="获取位置概率分布")
async def get_probability_distribution(position: str = Query(..., regex="^(hundreds|tens|units)$")):
    """获取指定位置的概率分布"""
    try:
        if not os.path.exists(DB_PATH):
            return {
                "status": "success",
                "data": _get_mock_probability_distribution(position),
                "message": "使用模拟数据（数据库不存在）"
            }
        
        conn = sqlite3.connect(DB_PATH)
        
        try:
            # 获取最新期号的概率分布
            query = f"""
                SELECT {position}, AVG(combined_probability) as avg_probability, COUNT(*) as count
                FROM fusion_predictions
                WHERE issue = (SELECT MAX(issue) FROM fusion_predictions)
                GROUP BY {position}
                ORDER BY {position}
            """
            
            distribution = pd.read_sql_query(query, conn)
            conn.close()
            
            if distribution.empty:
                return {
                    "status": "success",
                    "data": _get_mock_probability_distribution(position),
                    "message": "使用模拟数据（无分布数据）"
                }
            
            return {
                "status": "success",
                "data": distribution.to_dict('records'),
                "position": position
            }
            
        except Exception as e:
            conn.close()
            return {
                "status": "success",
                "data": _get_mock_probability_distribution(position),
                "message": f"使用模拟数据（查询失败: {str(e)}）"
            }
            
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取概率分布失败: {str(e)}")

@router.get("/performance-comparison", summary="获取性能对比数据")
async def get_performance_comparison(
    period: str = Query("week", regex="^(week|month|quarter)$"),
    metric: str = Query("accuracy", regex="^(accuracy|precision|recall|f1)$")
):
    """获取不同模型的性能对比数据"""
    try:
        if not os.path.exists(DB_PATH):
            return {
                "status": "success",
                "data": _get_mock_performance_comparison(period, metric),
                "message": "使用模拟数据（数据库不存在）"
            }

        conn = sqlite3.connect(DB_PATH)

        try:
            # 根据周期计算时间范围
            if period == "week":
                days = 7
            elif period == "month":
                days = 30
            else:  # quarter
                days = 90

            end_date = datetime.now()
            start_date = end_date - timedelta(days=days)

            # 获取性能对比数据（这里假设有模型性能表）
            query = """
                SELECT
                    model_name,
                    AVG(accuracy_score) as avg_accuracy,
                    AVG(precision_score) as avg_precision,
                    AVG(recall_score) as avg_recall,
                    AVG(f1_score) as avg_f1,
                    COUNT(*) as prediction_count
                FROM model_performance
                WHERE evaluation_date >= ? AND evaluation_date <= ?
                GROUP BY model_name
                ORDER BY avg_accuracy DESC
            """

            performance = pd.read_sql_query(
                query, conn,
                params=[start_date.isoformat(), end_date.isoformat()]
            )

            conn.close()

            if performance.empty:
                return {
                    "status": "success",
                    "data": _get_mock_performance_comparison(period, metric),
                    "message": "使用模拟数据（无性能数据）"
                }

            return {
                "status": "success",
                "data": performance.to_dict('records'),
                "period": period,
                "metric": metric
            }

        except Exception as e:
            conn.close()
            return {
                "status": "success",
                "data": _get_mock_performance_comparison(period, metric),
                "message": f"使用模拟数据（查询失败: {str(e)}）"
            }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"获取性能对比失败: {str(e)}")

def _get_mock_predictions(limit: int) -> List[Dict]:
    """获取模拟预测数据"""
    import random
    predictions = []
    for i in range(min(limit, 20)):
        predictions.append({
            'issue': '2025209',
            'prediction_rank': i + 1,
            'hundreds': random.randint(0, 9),
            'tens': random.randint(0, 9),
            'units': random.randint(0, 9),
            'sum_value': random.randint(0, 27),
            'span': random.randint(0, 9),
            'combined_probability': round(random.uniform(0.1, 0.9), 3),
            'confidence_level': random.choice(['high', 'medium', 'low']),
            'constraint_score': round(random.uniform(0.5, 1.0), 3),
            'created_at': datetime.now().isoformat()
        })
    return predictions

def _get_mock_history_data(days: int, page: int, page_size: int) -> List[Dict]:
    """获取模拟历史数据"""
    import random
    history = []
    total_records = min(days * 20, 200)  # 每天最多20条记录
    
    for i in range(min(page_size, total_records)):
        date_offset = random.randint(0, days)
        created_at = datetime.now() - timedelta(days=date_offset)
        
        history.append({
            'issue': f'2025{str(random.randint(1, 365)).zfill(3)}',
            'prediction_rank': random.randint(1, 20),
            'hundreds': random.randint(0, 9),
            'tens': random.randint(0, 9),
            'units': random.randint(0, 9),
            'sum_value': random.randint(0, 27),
            'span': random.randint(0, 9),
            'combined_probability': round(random.uniform(0.1, 0.9), 3),
            'confidence_level': random.choice(['high', 'medium', 'low']),
            'constraint_score': round(random.uniform(0.5, 1.0), 3),
            'created_at': created_at.isoformat()
        })
    
    return history

def _get_mock_statistics() -> Dict:
    """获取模拟统计数据"""
    import random
    return {
        "basic_stats": {
            "total_issues": random.randint(20, 50),
            "total_predictions": random.randint(400, 1000),
            "avg_probability": round(random.uniform(0.4, 0.8), 3),
            "last_prediction_time": datetime.now().isoformat()
        },
        "confidence_distribution": [
            {"confidence_level": "high", "count": random.randint(100, 200)},
            {"confidence_level": "medium", "count": random.randint(150, 300)},
            {"confidence_level": "low", "count": random.randint(50, 150)}
        ],
        "update_time": datetime.now().isoformat()
    }

def _get_mock_probability_distribution(position: str) -> List[Dict]:
    """获取模拟概率分布数据"""
    import random
    distribution = []
    for digit in range(10):
        distribution.append({
            position: digit,
            'avg_probability': round(random.uniform(0.05, 0.15), 3),
            'count': random.randint(1, 5)
        })
    return distribution

def _get_mock_accuracy_trends(days: int) -> List[Dict]:
    """获取模拟准确率趋势数据"""
    import random
    trends = []
    for i in range(days):
        date = datetime.now() - timedelta(days=i)
        trends.append({
            'date': date.strftime('%Y-%m-%d'),
            'total_predictions': random.randint(50, 200),
            'avg_probability': round(random.uniform(0.4, 0.8), 3),
            'high_confidence_count': random.randint(10, 50),
            'avg_constraint_score': round(random.uniform(0.6, 0.9), 3)
        })
    return trends

def _get_mock_performance_comparison(period: str, metric: str) -> List[Dict]:
    """获取模拟性能对比数据"""
    import random
    models = ['XGBoost', 'LightGBM', 'LSTM', '集成模型']
    comparison = []

    for model in models:
        comparison.append({
            'model_name': model,
            'avg_accuracy': round(random.uniform(0.6, 0.9), 3),
            'avg_precision': round(random.uniform(0.5, 0.8), 3),
            'avg_recall': round(random.uniform(0.5, 0.8), 3),
            'avg_f1': round(random.uniform(0.5, 0.8), 3),
            'prediction_count': random.randint(100, 1000)
        })

    return comparison
