import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![truck](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIGZpbGwtcnVsZT0iZXZlbm9kZCIgdmlld0JveD0iNjQgNjQgODk2IDg5NiIgZm9jdXNhYmxlPSJmYWxzZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cGF0aCBkPSJNNjA4IDE5MmEzMiAzMiAwIDAxMzIgMzJ2MTYwaDE3NC44MWEzMiAzMiAwIDAxMjYuNjggMTQuMzNsMTEzLjE5IDE3MC44NGEzMiAzMiAwIDAxNS4zMiAxNy42OFY2NzJhMzIgMzIgMCAwMS0zMiAzMmgtOTZjMCA3MC43LTU3LjMgMTI4LTEyOCAxMjhzLTEyOC01Ny4zLTEyOC0xMjhIMzg0YzAgNzAuNy01Ny4zIDEyOC0xMjggMTI4cy0xMjgtNTcuMy0xMjgtMTI4SDk2YTMyIDMyIDAgMDEtMzItMzJWMjI0YTMyIDMyIDAgMDEzMi0zMnpNMjU2IDY0MGE2NCA2NCAwIDAwMCAxMjhoMS4wNkE2NCA2NCAwIDAwMjU2IDY0MG00NDggMGE2NCA2NCAwIDAwMCAxMjhoMS4wNkE2NCA2NCAwIDAwNzA0IDY0MG05My42My0xOTJINjQwdjE0NS4xMkExMjcuNDMgMTI3LjQzIDAgMDE3MDQgNTc2YzQ3LjM4IDAgODguNzUgMjUuNzQgMTEwLjg4IDY0SDg5NnYtNDMuNTJ6TTUwMCA0NDhIMzMyYTEyIDEyIDAgMDAtMTIgMTJ2NDBhMTIgMTIgMCAwMDEyIDEyaDE2OGExMiAxMiAwIDAwMTItMTJ2LTQwYTEyIDEyIDAgMDAtMTItMTJNMzA4IDMyMEgyMDRhMTIgMTIgMCAwMC0xMiAxMnY0MGExMiAxMiAwIDAwMTIgMTJoMTA0YTEyIDEyIDAgMDAxMi0xMnYtNDBhMTIgMTIgMCAwMC0xMi0xMiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
