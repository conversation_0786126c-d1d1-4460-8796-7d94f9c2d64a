import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![tool](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTg2NS4zIDI0NC43Yy0uMy0uMy02MS4xIDU5LjgtMTgyLjEgMTgwLjZsLTg0LjktODQuOSAxODAuOS0xODAuOWMtOTUuMi01Ny4zLTIxNy41LTQyLjYtMjk2LjggMzYuN0EyNDQuNDIgMjQ0LjQyIDAgMDA0MTkgNDMybDEuOCA2LjctMjgzLjUgMjgzLjRjLTYuMiA2LjItNi4yIDE2LjQgMCAyMi42bDE0MS40IDE0MS40YzYuMiA2LjIgMTYuNCA2LjIgMjIuNiAwbDI4My4zLTI4My4zIDYuNyAxLjhjODMuNyAyMi4zIDE3My42LS45IDIzNi02My4zIDc5LjQtNzkuMyA5NC4xLTIwMS42IDM4LTI5Ni42eiIgLz48L3N2Zz4=) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
