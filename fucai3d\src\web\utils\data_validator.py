"""
数据验证工具 - 确保系统严格遵循"禁止虚拟数据"原则
"""

import sqlite3
import re
from typing import Dict, List, Any, Tuple
from datetime import datetime
import os

class DataValidator:
    """数据真实性验证器"""
    
    def __init__(self, db_path: str = "data/fucai3d.db"):
        self.db_path = db_path
        self.virtual_data_patterns = [
            r'^2024001$', r'^2024002$', r'^2024003$',  # 明显的虚拟期号
            r'^test\d+$', r'^demo\d+$', r'^sample\d+$',  # 测试数据模式
        ]
        self.virtual_keywords = [
            'test', 'demo', 'sample', 'example', 'mock',
            '测试', '示例', '样例', '演示', '虚拟', '模拟'
        ]
    
    def validate_real_data(self, data: List[Dict]) -> Tuple[bool, List[str]]:
        """
        验证数据来源真实性
        
        Args:
            data: 要验证的数据列表
            
        Returns:
            (is_valid, error_messages): 验证结果和错误信息
        """
        errors = []
        
        if not data:
            return True, []  # 空数据不算虚拟数据
        
        for i, record in enumerate(data):
            # 检查期号格式
            issue = record.get('issue', '')
            if self._is_virtual_issue(issue):
                errors.append(f"记录{i+1}: 检测到虚拟期号 '{issue}'")
            
            # 检查是否包含虚拟数据关键词
            record_str = str(record).lower()
            for keyword in self.virtual_keywords:
                if keyword in record_str:
                    errors.append(f"记录{i+1}: 检测到虚拟数据关键词 '{keyword}'")
                    break
            
            # 检查数据合理性
            if not self._is_reasonable_data(record):
                errors.append(f"记录{i+1}: 数据不合理，可能是虚拟数据")
        
        return len(errors) == 0, errors
    
    def _is_virtual_issue(self, issue: str) -> bool:
        """检查期号是否为虚拟数据"""
        if not issue:
            return False
            
        # 检查虚拟期号模式
        for pattern in self.virtual_data_patterns:
            if re.match(pattern, issue):
                return True
        
        # 检查期号格式合理性
        if len(issue) == 7 and issue.startswith('2025'):
            try:
                year = int(issue[:4])
                day_num = int(issue[4:])
                # 2025年的合理期号范围
                if year == 2025 and 1 <= day_num <= 365:
                    return False
            except ValueError:
                return True
        
        return True  # 其他格式都认为是虚拟数据
    
    def _is_reasonable_data(self, record: Dict) -> bool:
        """检查数据是否合理"""
        try:
            # 检查福彩3D数字范围
            hundreds = record.get('hundreds')
            tens = record.get('tens')
            units = record.get('units')
            
            if hundreds is not None and not (0 <= hundreds <= 9):
                return False
            if tens is not None and not (0 <= tens <= 9):
                return False
            if units is not None and not (0 <= units <= 9):
                return False
            
            # 检查时间戳合理性
            created_at = record.get('created_at')
            if created_at:
                try:
                    dt = datetime.fromisoformat(created_at.replace('Z', '+00:00'))
                    # 数据时间应该在合理范围内
                    if dt.year < 2020 or dt.year > 2030:
                        return False
                except:
                    return False
            
            return True
        except:
            return False
    
    def check_database_integrity(self) -> Dict[str, Any]:
        """检查数据库完整性和真实性"""
        if not os.path.exists(self.db_path):
            return {
                "status": "error",
                "message": "数据库文件不存在",
                "is_compliant": False
            }
        
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # 检查final_predictions表
            cursor.execute("SELECT * FROM final_predictions LIMIT 100")
            records = cursor.fetchall()
            
            # 获取列名
            cursor.execute("PRAGMA table_info(final_predictions)")
            columns = [col[1] for col in cursor.fetchall()]
            
            conn.close()
            
            # 转换为字典格式
            data = []
            for record in records:
                data.append(dict(zip(columns, record)))
            
            # 验证数据真实性
            is_valid, errors = self.validate_real_data(data)
            
            return {
                "status": "success",
                "is_compliant": is_valid,
                "total_records": len(data),
                "errors": errors,
                "message": "数据验证完成" if is_valid else f"发现{len(errors)}个问题"
            }
            
        except Exception as e:
            return {
                "status": "error",
                "message": f"数据库检查失败: {str(e)}",
                "is_compliant": False
            }
    
    def ensure_compliance(self, api_response: Dict) -> Dict[str, Any]:
        """确保API响应符合合规性要求"""
        # 检查响应中是否包含虚拟数据标识
        response_str = str(api_response).lower()
        
        violations = []
        
        # 检查是否包含"模拟数据"等字样
        mock_indicators = ['模拟数据', 'mock data', '虚拟数据', 'virtual data', 'fake data']
        for indicator in mock_indicators:
            if indicator in response_str:
                violations.append(f"响应包含虚拟数据标识: '{indicator}'")
        
        # 检查数据内容
        data = api_response.get('data', [])
        if data:
            is_valid, errors = self.validate_real_data(data)
            if not is_valid:
                violations.extend(errors)
        
        return {
            "is_compliant": len(violations) == 0,
            "violations": violations,
            "status": "compliant" if len(violations) == 0 else "violation"
        }
    
    def generate_compliance_report(self) -> Dict[str, Any]:
        """生成合规性报告"""
        report = {
            "timestamp": datetime.now().isoformat(),
            "database_check": self.check_database_integrity(),
            "compliance_status": "unknown"
        }
        
        # 确定整体合规状态
        db_compliant = report["database_check"].get("is_compliant", False)
        
        if db_compliant:
            report["compliance_status"] = "compliant"
            report["message"] = "✅ 系统完全合规，只使用真实数据"
        else:
            report["compliance_status"] = "violation"
            report["message"] = "❌ 发现合规性违规，存在虚拟数据"
        
        return report

# 全局验证器实例
validator = DataValidator()

def validate_api_response(response: Dict) -> Dict[str, Any]:
    """验证API响应的合规性"""
    return validator.ensure_compliance(response)

def check_system_compliance() -> Dict[str, Any]:
    """检查系统整体合规性"""
    return validator.generate_compliance_report()

# 装饰器：自动验证API响应
def ensure_real_data(func):
    """装饰器：确保API只返回真实数据"""
    def wrapper(*args, **kwargs):
        result = func(*args, **kwargs)
        
        # 验证响应合规性
        compliance = validate_api_response(result)
        
        if not compliance["is_compliant"]:
            # 如果不合规，抛出异常
            from fastapi import HTTPException
            raise HTTPException(
                status_code=500, 
                detail=f"合规性违规: {'; '.join(compliance['violations'])}"
            )
        
        return result
    return wrapper
