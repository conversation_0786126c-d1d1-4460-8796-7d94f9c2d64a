import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![unlock](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTIzMiA4NDBoNTYwVjUzNkgyMzJ2MzA0em0yODAtMjI2YTQ4LjAxIDQ4LjAxIDAgMDEyOCA4N3Y1M2MwIDQuNC0zLjYgOC04IDhoLTQwYy00LjQgMC04LTMuNi04LTh2LTUzYTQ4LjAxIDQ4LjAxIDAgMDEyOC04N3oiIGZpbGw9IiNlNmY0ZmYiIC8+PHBhdGggZD0iTTQ4NCA3MDF2NTNjMCA0LjQgMy42IDggOCA4aDQwYzQuNCAwIDgtMy42IDgtOHYtNTNhNDguMDEgNDguMDEgMCAxMC01NiAweiIgZmlsbD0iIzE2NzdmZiIgLz48cGF0aCBkPSJNODMyIDQ2NEgzMzJWMjQwYzAtMzAuOSAyNS4xLTU2IDU2LTU2aDI0OGMzMC45IDAgNTYgMjUuMSA1NiA1NnY2OGMwIDQuNCAzLjYgOCA4IDhoNTZjNC40IDAgOC0zLjYgOC04di02OGMwLTcwLjctNTcuMy0xMjgtMTI4LTEyOEgzODhjLTcwLjcgMC0xMjggNTcuMy0xMjggMTI4djIyNGgtNjhjLTE3LjcgMC0zMiAxNC4zLTMyIDMydjM4NGMwIDE3LjcgMTQuMyAzMiAzMiAzMmg2NDBjMTcuNyAwIDMyLTE0LjMgMzItMzJWNDk2YzAtMTcuNy0xNC4zLTMyLTMyLTMyem0tNDAgMzc2SDIzMlY1MzZoNTYwdjMwNHoiIGZpbGw9IiMxNjc3ZmYiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
