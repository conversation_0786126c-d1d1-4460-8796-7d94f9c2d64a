import React, { useState, useEffect } from 'react'
import { Card, Select, Spin, Alert, Row, Col, Statistic } from 'antd'
import { LineChart, Line, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, AreaChart, Area } from 'recharts'
import * as Icons from '@ant-design/icons'

const { TrendingUpOutlined, TrendingDownOutlined, MinusOutlined } = Icons
import axios from 'axios'

const { Option } = Select

interface MetricData {
  value: number
  threshold?: number
  status: string
  timestamp: string
}

interface PerformanceData {
  [component: string]: {
    [metric: string]: MetricData[]
  }
}

const PerformanceMetrics: React.FC = () => {
  const [data, setData] = useState<PerformanceData>({})
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [timeRange, setTimeRange] = useState(24) // 小时
  const [selectedComponent, setSelectedComponent] = useState<string>('all')

  useEffect(() => {
    fetchPerformanceData()
  }, [timeRange])

  const fetchPerformanceData = async () => {
    try {
      setLoading(true)
      setError(null)
      
      const response = await axios.get(`/api/monitoring/metrics?hours=${timeRange}`)
      
      if (response.data.status === 'success') {
        setData(response.data.data)
        
        // 如果没有选择组件，默认选择第一个
        if (selectedComponent === 'all' && Object.keys(response.data.data).length > 0) {
          setSelectedComponent(Object.keys(response.data.data)[0])
        }
      } else {
        setError(response.data.message || '获取性能数据失败')
      }
    } catch (err) {
      setError('连接监控服务失败')
      console.error('获取性能数据失败:', err)
    } finally {
      setLoading(false)
    }
  }

  const formatChartData = (metricData: MetricData[]) => {
    return metricData.map(item => ({
      ...item,
      time: new Date(item.timestamp).toLocaleTimeString(),
      timestamp: item.timestamp
    })).sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime())
  }

  const calculateTrend = (metricData: MetricData[]) => {
    if (metricData.length < 2) return 'stable'
    
    const recent = metricData.slice(-5) // 最近5个数据点
    const avg1 = recent.slice(0, Math.ceil(recent.length / 2)).reduce((sum, item) => sum + item.value, 0) / Math.ceil(recent.length / 2)
    const avg2 = recent.slice(Math.ceil(recent.length / 2)).reduce((sum, item) => sum + item.value, 0) / Math.floor(recent.length / 2)
    
    const change = ((avg2 - avg1) / avg1) * 100
    
    if (change > 5) return 'up'
    if (change < -5) return 'down'
    return 'stable'
  }

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'up': return <TrendingUpOutlined style={{ color: '#52c41a' }} />
      case 'down': return <TrendingDownOutlined style={{ color: '#ff4d4f' }} />
      default: return <MinusOutlined style={{ color: '#1890ff' }} />
    }
  }

  const getLatestValue = (metricData: MetricData[]) => {
    if (metricData.length === 0) return null
    return metricData[metricData.length - 1]
  }

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload
      return (
        <div style={{
          background: 'white',
          border: '1px solid #d9d9d9',
          borderRadius: '6px',
          padding: '12px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.15)'
        }}>
          <p style={{ margin: 0, fontWeight: 'bold' }}>
            时间: {label}
          </p>
          <p style={{ margin: '4px 0 0 0', color: '#1890ff' }}>
            数值: {data.value}
          </p>
          {data.threshold && (
            <p style={{ margin: '4px 0 0 0', color: '#faad14' }}>
              阈值: {data.threshold}
            </p>
          )}
          <p style={{ margin: '4px 0 0 0', color: data.status === 'normal' ? '#52c41a' : '#ff4d4f' }}>
            状态: {data.status === 'normal' ? '正常' : '异常'}
          </p>
        </div>
      )
    }
    return null
  }

  if (loading) {
    return (
      <Card title="📈 性能指标" style={{ minHeight: 400 }}>
        <div style={{ textAlign: 'center', padding: '50px' }}>
          <Spin size="large" />
          <p style={{ marginTop: 16 }}>正在加载性能数据...</p>
        </div>
      </Card>
    )
  }

  if (error) {
    return (
      <Card title="📈 性能指标" style={{ minHeight: 400 }}>
        <Alert
          message="性能数据加载失败"
          description={error}
          type="error"
          showIcon
        />
      </Card>
    )
  }

  const components = Object.keys(data)
  const currentData = selectedComponent !== 'all' ? data[selectedComponent] : {}
  const metrics = Object.keys(currentData)

  return (
    <Card 
      title="📈 性能指标"
      extra={
        <div style={{ display: 'flex', gap: 16 }}>
          <Select
            value={selectedComponent}
            onChange={setSelectedComponent}
            style={{ width: 150 }}
            size="small"
          >
            {components.map(component => (
              <Option key={component} value={component}>
                {component}
              </Option>
            ))}
          </Select>
          <Select
            value={timeRange}
            onChange={setTimeRange}
            style={{ width: 100 }}
            size="small"
          >
            <Option value={1}>1小时</Option>
            <Option value={6}>6小时</Option>
            <Option value={24}>24小时</Option>
            <Option value={168}>7天</Option>
          </Select>
        </div>
      }
    >
      {components.length === 0 ? (
        <div style={{ textAlign: 'center', padding: '50px', color: '#999' }}>
          <div style={{ fontSize: '48px', marginBottom: 16 }}>📊</div>
          <div>暂无性能数据</div>
          <div style={{ fontSize: '12px', marginTop: 8 }}>
            请等待系统收集性能指标
          </div>
        </div>
      ) : (
        <>
          {/* 指标概览 */}
          <Row gutter={[16, 16]} style={{ marginBottom: 24 }}>
            {metrics.slice(0, 4).map(metric => {
              const metricData = currentData[metric] || []
              const latest = getLatestValue(metricData)
              const trend = calculateTrend(metricData)
              
              return (
                <Col key={metric} xs={12} sm={6}>
                  <Card size="small" style={{ textAlign: 'center' }}>
                    <Statistic
                      title={metric}
                      value={latest?.value || 0}
                      precision={2}
                      prefix={getTrendIcon(trend)}
                      valueStyle={{ 
                        fontSize: '16px',
                        color: latest?.status === 'normal' ? '#52c41a' : '#ff4d4f'
                      }}
                    />
                  </Card>
                </Col>
              )
            })}
          </Row>

          {/* 性能图表 */}
          {metrics.map(metric => {
            const metricData = currentData[metric] || []
            const chartData = formatChartData(metricData)
            
            if (chartData.length === 0) return null

            return (
              <Card key={metric} title={metric} size="small" style={{ marginBottom: 16 }}>
                <div style={{ height: 200 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    <AreaChart data={chartData}>
                      <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                      <XAxis 
                        dataKey="time" 
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: '#d9d9d9' }}
                      />
                      <YAxis 
                        tick={{ fontSize: 12 }}
                        axisLine={{ stroke: '#d9d9d9' }}
                      />
                      <Tooltip content={<CustomTooltip />} />
                      <Area
                        type="monotone"
                        dataKey="value"
                        stroke="#1890ff"
                        fill="#1890ff"
                        fillOpacity={0.1}
                        strokeWidth={2}
                      />
                      {chartData[0]?.threshold && (
                        <Line
                          type="monotone"
                          dataKey="threshold"
                          stroke="#faad14"
                          strokeDasharray="5 5"
                          dot={false}
                        />
                      )}
                    </AreaChart>
                  </ResponsiveContainer>
                </div>
                
                <div style={{ 
                  marginTop: 8, 
                  display: 'flex', 
                  justifyContent: 'space-between',
                  fontSize: '12px',
                  color: '#666'
                }}>
                  <span>数据点: {chartData.length}</span>
                  <span>
                    状态: {chartData.filter(d => d.status === 'normal').length} 正常 / {chartData.filter(d => d.status !== 'normal').length} 异常
                  </span>
                </div>
              </Card>
            )
          })}
        </>
      )}
    </Card>
  )
}

export default PerformanceMetrics
