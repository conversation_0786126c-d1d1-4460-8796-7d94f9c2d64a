import * as React from 'react';
import { AntdIconProps } from '../components/AntdIcon';
/**![tags](data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAiIGhlaWdodD0iNTAiIGZpbGw9IiNjYWNhY2EiIHZpZXdCb3g9IjY0IDY0IDg5NiA4OTYiIGZvY3VzYWJsZT0iZmFsc2UiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTQ4My4yIDc5MC4zTDg2MS40IDQxMmMxLjctMS43IDIuNS00IDIuMy02LjNsLTI1LjUtMzAxLjRjLS43LTcuOC02LjgtMTMuOS0xNC42LTE0LjZMNTIyLjIgNjQuM2MtMi4zLS4yLTQuNy42LTYuMyAyLjNMMTM3LjcgNDQ0LjhhOC4wMyA4LjAzIDAgMDAwIDExLjNsMzM0LjIgMzM0LjJjMy4xIDMuMiA4LjIgMy4yIDExLjMgMHptNjIuNi02NTEuN2wyMjQuNiAxOSAxOSAyMjQuNkw0NzcuNSA2OTQgMjMzLjkgNDUwLjVsMzExLjktMzExLjl6bTYwLjE2IDE4Ni4yM2E0OCA0OCAwIDEwNjcuODgtNjcuODkgNDggNDggMCAxMC02Ny44OCA2Ny44OXpNODg5LjcgNTM5LjhsLTM5LjYtMzkuNWE4LjAzIDguMDMgMCAwMC0xMS4zIDBsLTM2MiAzNjEuMy0yMzcuNi0yMzdhOC4wMyA4LjAzIDAgMDAtMTEuMyAwbC0zOS42IDM5LjVhOC4wMyA4LjAzIDAgMDAwIDExLjNsMjQzLjIgMjQyLjggMzkuNiAzOS41YzMuMSAzLjEgOC4yIDMuMSAxMS4zIDBsNDA3LjMtNDA2LjZjMy4xLTMuMSAzLjEtOC4yIDAtMTEuM3oiIC8+PC9zdmc+) */
declare const RefIcon: React.ForwardRefExoticComponent<Omit<AntdIconProps, 'ref'> & React.RefAttributes<HTMLSpanElement>>;
export default RefIcon;
